* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
header {
    background: linear-gradient(135deg, #8B4513 0%, #A0522D 50%, #CD853F 100%);
    color: white;
    padding: 1rem 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.logo {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.logo h1 {
    font-size: 2rem;
    font-weight: bold;
    color: #FFD700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.logo .tagline {
    font-size: 0.9rem;
    color: #F5DEB3;
    margin-top: -5px;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-links a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: 20px;
}

.nav-links a:hover {
    color: #FFD700;
    background: rgba(255, 215, 0, 0.1);
}

.nav-icons {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.nav-icons a {
    color: white;
    font-size: 1.2rem;
    text-decoration: none;
    transition: color 0.3s ease;
    position: relative;
}

.nav-icons a:hover {
    color: #FFD700;
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #FF6B6B;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #8B4513 0%, #A0522D 50%, #CD853F 100%);
    color: white;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding-top: 120px;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="gems" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="rgba(255,215,0,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23gems)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
}

.hero-content h2 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    animation: fadeInUp 1s ease;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    color: #FFD700;
}

.hero-content p {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    animation: fadeInUp 1s ease 0.2s both;
    line-height: 1.8;
    color: #F5DEB3;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease 0.4s both;
}

.cta-button {
    padding: 15px 35px;
    border: none;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.cta-button.primary {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #8B4513;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.cta-button.secondary {
    background: transparent;
    color: #FFD700;
    border: 2px solid #FFD700;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
}

.hero-features {
    display: flex;
    gap: 3rem;
    margin-top: 4rem;
    justify-content: center;
    flex-wrap: wrap;
    position: relative;
    z-index: 2;
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    color: #F5DEB3;
    animation: fadeInUp 1s ease 0.6s both;
}

.feature-item i {
    font-size: 2.5rem;
    color: #FFD700;
    margin-bottom: 1rem;
}

.feature-item span {
    font-weight: 600;
    font-size: 1.1rem;
}

/* Sections */
.section {
    padding: 80px 0;
}

.section:nth-child(even) {
    background: linear-gradient(135deg, #FFF8DC 0%, #F5F5DC 100%);
}

.section h2 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 1rem;
    color: #8B4513;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.section-desc {
    text-align: center;
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.product-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(139, 69, 19, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.product-overlay button {
    background: #FFD700;
    color: #8B4513;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.product-overlay button:hover {
    background: #FFA500;
    transform: scale(1.05);
}

.product-info {
    padding: 1.5rem;
}

.product-info h3 {
    color: #8B4513;
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
}

.product-desc {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.product-price {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.current-price {
    font-size: 1.4rem;
    font-weight: bold;
    color: #8B4513;
}

.old-price {
    font-size: 1rem;
    color: #999;
    text-decoration: line-through;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.product-rating i {
    color: #FFD700;
    font-size: 0.9rem;
}

.product-rating span {
    color: #666;
    font-size: 0.9rem;
}

.view-all {
    text-align: center;
    margin-top: 2rem;
}

.view-all-btn {
    background: linear-gradient(45deg, #8B4513, #A0522D);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-all-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(139, 69, 19, 0.3);
}

/* Gems Categories */
.gems-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.gem-category {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    text-align: center;
}

.gem-category:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.gem-image {
    height: 200px;
    overflow: hidden;
}

.gem-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gem-category:hover .gem-image img {
    transform: scale(1.1);
}

.gem-category h3 {
    color: #8B4513;
    font-size: 1.4rem;
    margin: 1.5rem 0 1rem;
}

.gem-category p {
    color: #666;
    margin-bottom: 1rem;
    padding: 0 1rem;
}

.price-range {
    display: inline-block;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #8B4513;
    padding: 8px 20px;
    border-radius: 20px;
    font-weight: bold;
    margin-bottom: 1.5rem;
}

/* Rosaries */
.rosaries-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
}

.rosary-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
}

.rosary-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.rosary-image {
    height: 300px;
    overflow: hidden;
}

.rosary-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.rosary-card:hover .rosary-image img {
    transform: scale(1.05);
}

.rosary-info {
    padding: 2rem;
    flex-grow: 1;
}

.rosary-info h3 {
    color: #8B4513;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.rosary-info p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.rosary-features {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.rosary-features span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #8B4513;
    font-size: 0.9rem;
}

.rosary-features i {
    color: #28a745;
    font-size: 0.8rem;
}

.rosary-price {
    font-size: 1.8rem;
    font-weight: bold;
    color: #8B4513;
    text-align: center;
    background: linear-gradient(135deg, #FFF8DC, #F5DEB3);
    padding: 1rem;
    border-radius: 10px;
    margin-top: auto;
}

/* About Section */
.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h3 {
    color: #8B4513;
    font-size: 2rem;
    margin-bottom: 1.5rem;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #666;
    margin-bottom: 2rem;
}

.about-features {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.about-feature {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.about-feature i {
    font-size: 2rem;
    color: #FFD700;
    margin-top: 0.5rem;
}

.about-feature h4 {
    color: #8B4513;
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.about-feature p {
    color: #666;
    line-height: 1.6;
    margin: 0;
}

.about-image {
    text-align: center;
}

.about-image img {
    width: 100%;
    max-width: 500px;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

/* Contact Section */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info h3 {
    color: #8B4513;
    font-size: 1.8rem;
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.contact-item i {
    font-size: 1.5rem;
    color: #FFD700;
    margin-top: 0.2rem;
    width: 30px;
}

.contact-item h4 {
    color: #8B4513;
    font-size: 1.1rem;
    margin-bottom: 0.3rem;
}

.contact-item p {
    color: #666;
    margin: 0;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background: linear-gradient(45deg, #8B4513, #A0522D);
    color: white;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(139, 69, 19, 0.3);
}

/* Contact Form */
.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.contact-form h3 {
    color: #8B4513;
    font-size: 1.8rem;
    margin-bottom: 2rem;
    text-align: center;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #8B4513;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #ddd;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #FFD700;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.2);
}

.contact-form button {
    background: linear-gradient(45deg, #8B4513, #A0522D);
    color: white;
    padding: 15px 40px;
    border: none;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.contact-form button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(139, 69, 19, 0.3);
}

/* Footer */
footer {
    background: linear-gradient(135deg, #2C1810 0%, #8B4513 100%);
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    color: #FFD700;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-section h4 {
    color: #FFD700;
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.footer-section p {
    color: #F5DEB3;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #F5DEB3;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #FFD700;
}

.footer-social {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.footer-social a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 215, 0, 0.2);
    color: #FFD700;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-social a:hover {
    background: #FFD700;
    color: #8B4513;
    transform: translateY(-2px);
}

.contact-info-footer {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.contact-info-footer p {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
}

.contact-info-footer i {
    color: #FFD700;
    width: 20px;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 215, 0, 0.3);
    padding-top: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-bottom p {
    color: #F5DEB3;
    margin: 0;
}

.payment-methods {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.payment-methods i {
    font-size: 2rem;
    color: #FFD700;
    transition: transform 0.3s ease;
}

.payment-methods i:hover {
    transform: scale(1.1);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .rosaries-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .hero-content h2 {
        font-size: 2.5rem;
    }

    .hero-content p {
        font-size: 1.1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-features {
        gap: 2rem;
    }

    .section h2 {
        font-size: 2.2rem;
    }

    .products-grid,
    .gems-categories {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-content h2 {
        font-size: 2rem;
    }

    .section {
        padding: 60px 0;
    }

    .section h2 {
        font-size: 1.8rem;
    }

    .cta-button {
        padding: 12px 25px;
        font-size: 1rem;
    }

    .product-card,
    .gem-category,
    .rosary-card {
        margin: 0 10px;
    }
}
